import { atom } from 'jotai';
import { ICurrentClocState } from '@cloc/types';
import { getWeekStartAndEnd } from '@cloc/ui';
import { DateRange } from 'react-day-picker';
import { defaultTheme } from '../../themes/themes';
import { useTimerStatus } from '@hooks/useTimerStatus';
import { useOrganizationTeams } from '@hooks/useOrganisationTeams';
import { useOrganizationProjects } from '@hooks/useOrganisationProjects';
import { useMyTasks } from '@hooks/useMyTasks';
import { useAuthUser } from '@hooks/useAuthUser';
import useFontSelector from '@hooks/useFontSelector';
import { FONT_OPTIONS } from '../../font/font';
import { useEmployeeOrganization } from '@hooks/useEmployeeOrganisation';
import { useUserPermission } from '@hooks/useUserPermission';
import { useMember } from '@hooks/useMember';
import { useStatisticsCounts } from '@hooks/useStatisticsCounts';
import { useUserOrganization } from '@hooks/useUserOrganization';

const getInitialFont = () => {
	if (typeof window !== 'undefined') {
		return localStorage.getItem('selected-font') || FONT_OPTIONS[0].value;
	}
	return FONT_OPTIONS[0].value;
};

// Atoms

export const currentClocStateAtom = atom<ICurrentClocState>({
	clientId: null,
	projectId: null,
	taskId: null,
	organizationTeamId: null
});

export const reportDatesAtom = atom<DateRange | undefined>({
	from: getWeekStartAndEnd(new Date()).start,
	to: getWeekStartAndEnd(new Date()).end
});

export const appliedThemeAtom = atom(defaultTheme);

export const timerStatusAtom = atom<ReturnType<typeof useTimerStatus>>({
	data: { duration: 0, lastLog: null, running: false },
	loading: false
});

export const statisticsCountsAtom = atom<ReturnType<typeof useStatisticsCounts>>({
	data: {
		employeesCount: 0,
		projectsCount: 0,
		todayActivities: 0,
		todayDuration: 0,
		weekActivities: 0,
		weekDuration: 0
	},
	loading: false
});

export const userOrganizationsAtom = atom<ReturnType<typeof useUserOrganization>>({ data: null, loading: false });

export const selectedFontAtom = atom<ReturnType<typeof useFontSelector>>({
	selectedFont: getInitialFont(),
	fontOptions: FONT_OPTIONS,
	setSelectedFont: () => {}
});

export const organizationTeamsAtom = atom<ReturnType<typeof useOrganizationTeams>>({
	data: null,
	loading: false
});

export const organizationProjectsAtom = atom<ReturnType<typeof useOrganizationProjects>>({
	data: null,
	loading: false
});

export const selectedEmployeeAtom = atom<string>('all');

export const selectedTeamAtom = atom<string>('all');

export const selectedOrganizationAtom = atom<string>('');

export const membersAtom = atom<ReturnType<typeof useMember>>({
	data: null,
	loading: false
});

export const organizationClientsAtom = atom<ReturnType<typeof useEmployeeOrganization>>({ data: null, loading: false });

export const tasksAtom = atom<ReturnType<typeof useMyTasks>>({ data: null, loading: false });

export const userAtom = atom<ReturnType<typeof useAuthUser>>({ data: null, loading: false });

export const userPermissionsAtom = atom<ReturnType<typeof useUserPermission>>({ data: null, loading: false });

export const accessTokenAtom = atom<string>('');

export const timerLoadingAtom = atom<boolean>(false);

export const fullWidthState = atom<boolean>(true);

import { VariantProps } from 'class-variance-authority';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';
import { useActivitiesStats } from '@hooks/useActivitiesStatistics';

interface IClocAppsUrlListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocAppsUrlList = ({ variant, size, className }: IClocAppsUrlListProps) => {
	const { t } = useTranslation();
	const {
		authenticatedUser: user,
		token: accessToken,
		selectedEmployee,
		selectedTeam,
		selectedOrganization: organizationId,
		reportDates
	} = useClocContext();

	const { data: activitiesStats, loading: activitiesStatsLoading } = useActivitiesStats(
		user,
		accessToken,
		selectedEmployee,
		selectedTeam,
		organizationId,
		reportDates
	);

	return (
		<ClocBaseList
			stats={{ data: activitiesStats, loading: activitiesStatsLoading }}
			title={t('COMMON.apps_and_url')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocAppsUrlList };

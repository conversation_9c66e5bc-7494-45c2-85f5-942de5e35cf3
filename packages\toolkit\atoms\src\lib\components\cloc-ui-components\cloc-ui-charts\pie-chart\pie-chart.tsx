'use client';

import { Pie, <PERSON><PERSON><PERSON> as RechartPieChart } from 'recharts';

import { ChartTooltip, ChartTooltipContent, ChartLegend, ChartLegendContent } from '../chart';
import { IChartProps } from '@cloc/types';
import { ChartContainer } from '../chart-container';
import { useClocContext } from '@lib/context/cloc-context';
import { generateRandomColors } from '@cloc/ui';

const PieChart: React.FC<IChartProps> = ({ data, config, color }) => {
	const { config: contextConfig } = useClocContext();
	const finalConfig = config || contextConfig;

	const hasData = data.length > 0;
	const colors = color && hasData ? generateRandomColors(color, Object.keys(data[0]).length) : '';

	return (
		<ChartContainer config={finalConfig}>
			<RechartPieChart>
				<ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />

				<ChartLegend
					className="w-full flex flex-wrap justify-center items-center"
					content={<ChartLegendContent />}
				/>

				<ChartTooltip content={<ChartTooltipContent hideLabel />} />
				<Pie
					data={data}
					fill={colors[0]}
					dataKey={hasData ? Object.keys(data[0])[1] : ''}
					label
					nameKey={hasData ? Object.keys(data[0])[0] : ''}
				/>
			</RechartPieChart>
		</ChartContainer>
	);
};
PieChart.displayName = 'PieChart';
export { PieChart };

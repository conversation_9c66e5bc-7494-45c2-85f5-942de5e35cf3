import { VariantProps } from 'class-variance-authority';
import { useClocContext } from '@lib/context/cloc-context';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';
import { useTasksStats } from '@hooks/useTasksStatistics';

interface IClocTasksListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocTasksList = ({ variant, size, className }: IClocTasksListProps) => {
	const { t } = useTranslation();
	const {
		authenticatedUser: user,
		token: accessToken,
		selectedEmployee,
		selectedTeam,
		selectedOrganization: organizationId,
		reportDates
	} = useClocContext();

	const { data: tasksStats, loading: tasksStatsLoading } = useTasksStats(
		user,
		accessToken,
		selectedEmployee,
		selectedTeam,
		organizationId,
		reportDates
	);

	return (
		<ClocBaseList
			stats={{ data: tasksStats, loading: tasksStatsLoading }}
			title={t('COMMON.task')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocTasksList };

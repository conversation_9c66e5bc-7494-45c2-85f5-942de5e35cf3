'use client';

import {
	ClocLoginDialog,
	ClocProjectsList,
	ClocRegistrationForm,
	ClocThemeToggle,
	ClocDailyActivityDisplayer,
	useClocContext,
	ClocWeeklyActivityDisplayer,
	ClocWorkedProjectDisplayer,
	ClocTasksList,
	ClocAppsUrlList,
	ModernCloc,
	ClocDailyWorkedTimeDisplayer,
	ClocWeeklyWorkedTimeDisplayer,
	ClocTrackingFilter,
	ClocTrackingSessionInsight,
	ClocTrackingClickInsight,
	ClocTrackingHeatmap,
	ClocTrackingSessionReplay,
	BasicClocReport
} from '@cloc/atoms';
import { Button, Dialog, ThemedButton } from '@cloc/ui';
import { Suspense } from 'react';

export default function Home() {
	const { authenticatedUser: user } = useClocContext();

	return (
		<div className="p-8">
			<div className="my-20 flex flex-col gap-6 text-xl items-center ">
				<h1 className=" font-bold text-center text-6xl tracking-tighter">Cloc NextJs Boilerplate</h1>
				<p className="text-center text-[#777777] dark:text-gray-400">
					Discover Cloc NextJs Boilerplate and themes to jumpstart your application or website build.
				</p>

				<ClocThemeToggle />

				<div className={'flex gap-3 items-center'}>
					<ClocDailyWorkedTimeDisplayer />
					<ClocWeeklyWorkedTimeDisplayer />
					<ClocWeeklyActivityDisplayer />
					<ClocDailyActivityDisplayer />
					<ClocWorkedProjectDisplayer />
				</div>
				{!user && (
					<div className="flex gap-6">
						<Dialog
							trigger={
								<ThemedButton size={'lg'} className="min-w-40">
									Register Now
								</ThemedButton>
							}
						>
							<ClocRegistrationForm />
						</Dialog>

						<ClocLoginDialog
							trigger={
								<Button
									size={'lg'}
									variant={'outline'}
									className="relative min-w-40 hover:scale-105 transition-all "
								>
									Login
								</Button>
							}
						/>
					</div>
				)}
			</div>
			<div className="flex m-5 box-border gap-2 flex-wrap  justify-center sm:items-start">
				<ModernCloc expanded={false} showProgress={true} />
				<BasicClocReport type="bar-vertical" className=" shadow-none" />
				<BasicClocReport type="bar" className=" shadow-none" />
				<BasicClocReport type="area" className=" shadow-none" />
				<BasicClocReport type="line" className=" shadow-none" />
				<BasicClocReport type="pie" className=" shadow-none" />
				<BasicClocReport type="radar" className=" shadow-none" />
				<BasicClocReport type="radial" className=" shadow-none" />
				<BasicClocReport type="tooltip" className=" shadow-none" />
				<Suspense fallback={<div>Loading...</div>}>
					<ModernCloc expanded showProgress />
				</Suspense>
				<ClocProjectsList />
				<ClocTasksList />
				<ClocAppsUrlList />
				<ClocTrackingFilter className="w-3/4" />
				<ClocTrackingClickInsight />
				<ClocTrackingSessionInsight />
				<ClocTrackingHeatmap />
				<ClocTrackingSessionReplay />
			</div>
		</div>
	);
}

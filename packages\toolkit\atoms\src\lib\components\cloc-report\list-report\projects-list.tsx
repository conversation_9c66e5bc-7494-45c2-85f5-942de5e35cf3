import { VariantProps } from 'class-variance-authority';
import { ClocBaseList, clocBaseListVariants } from './base';
import { useTranslation } from 'react-i18next';
import { useClocContext } from '@lib/context/cloc-context';
import { useProjectsStats } from '@hooks/useProjectsStatistics';

interface IClocProjectsListProps extends VariantProps<typeof clocBaseListVariants> {
	className?: string;
}

const ClocProjectsList = ({ variant, size, className }: IClocProjectsListProps) => {
	const { t } = useTranslation();

	const {
		authenticatedUser: user,
		token: accessToken,
		selectedEmployee,
		selectedTeam,
		selectedOrganization: organizationId,
		reportDates
	} = useClocContext();

	const { data: projectsStats, loading: projectsStatsLoading } = useProjectsStats(
		user,
		accessToken,
		selectedEmployee,
		selectedTeam,
		organizationId,
		reportDates
	);

	return (
		<ClocBaseList
			stats={{ data: projectsStats, loading: projectsStatsLoading }}
			title={t('COMMON.project')}
			className={className}
			variant={variant}
			size={size}
		/>
	);
};

export { ClocProjectsList };
